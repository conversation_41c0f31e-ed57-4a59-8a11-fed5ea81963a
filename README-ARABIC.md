# تشغيل موقع Laravel - Rocket LMS

## المتطلبات الأساسية
- XAMPP مع PHP 8.1+ (حاليًا لديك PHP 8.0.30)
- MySQL Database
- Composer

## طرق تشغيل الموقع

### الطريقة الأولى: استخدام Apache (الأسهل)

1. تأكد من تشغيل XAMPP Control Panel
2. ابدأ تشغيل Apache و MySQL
3. افتح المتصفح واذهب إلى:
   - `http://localhost/manssultan/public`
   - أو `http://127.0.0.1/manssultan/public`

### الطريقة الثانية: استخدام ملف Batch

1. انقر نقرًا مزدوجًا على ملف `start-website.bat`
2. اتبع التعليمات في النافذة

### الطريقة الثالثة: استخدام سطر الأوامر

```bash
# انتقل إلى مجلد الموقع
cd C:\xampp\htdocs\manssultan

# شغل الخادم المدمج
C:\xampp\php\php.exe -S localhost:8000 -t public
```

## فحص المتطلبات

لفحص متطلبات الخادم، اذهب إلى:
`http://localhost/manssultan/public/check.php`

## حل المشاكل الشائعة

### مشكلة إصدار PHP
- الموقع يحتاج PHP 8.1+ لكن XAMPP يحتوي على PHP 8.0.30
- **الحل**: حدث XAMPP إلى أحدث إصدار

### مشكلة قاعدة البيانات
- تأكد من أن MySQL يعمل في XAMPP
- تأكد من وجود قاعدة البيانات `messultan`
- تحقق من بيانات الاتصال في ملف `.env`

### مشكلة الصلاحيات
- تأكد من أن مجلدات `storage` و `bootstrap/cache` قابلة للكتابة

## إعدادات قاعدة البيانات

```
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=messultan
DB_USERNAME=test
DB_PASSWORD=Qazqaz123
```

## روابط مفيدة

- الموقع الرئيسي: `http://localhost/manssultan/public`
- فحص المتطلبات: `http://localhost/manssultan/public/check.php`
- لوحة تحكم XAMPP: `http://localhost/xampp`

## ملاحظات مهمة

1. تم تعديل ملف `vendor/composer/platform_check.php` للسماح بتشغيل الموقع مع PHP 8.0
2. قد تواجه بعض المشاكل بسبب عدم التوافق مع PHP 8.0
3. يُنصح بشدة بتحديث XAMPP إلى إصدار يحتوي على PHP 8.1+

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من سجلات الأخطاء في XAMPP
2. تأكد من تشغيل Apache و MySQL
3. تحقق من إعدادات `.env`
4. راجع ملف `check.php` للتأكد من المتطلبات
