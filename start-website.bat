@echo off
echo ========================================
echo    تشغيل موقع Laravel - Starting Website
echo ========================================
echo.

REM Set PHP path from XAMPP
set PHP_PATH=C:\xampp\php\php.exe

REM Check if PHP exists
if not exist "%PHP_PATH%" (
    echo خطأ: لم يتم العثور على PHP في المسار المحدد
    echo Error: PHP not found at %PHP_PATH%
    echo تأكد من تثبيت XAMPP بشكل صحيح
    echo Please make sure XAMPP is installed correctly.
    pause
    exit /b 1
)

REM Display PHP version
echo استخدام إصدار PHP التالي - Using PHP version:
"%PHP_PATH%" --version
echo.

echo بدء تشغيل الموقع - Starting the website...
echo.

REM Method 1: Try using Apache (recommended)
echo الطريقة الأولى: استخدام Apache - Method 1: Using Apache
echo.
echo يمكنك الوصول للموقع عبر:
echo You can access the website via:
echo.
echo 1. http://localhost/manssultan/public
echo 2. http://127.0.0.1/manssultan/public
echo.
echo إذا لم تعمل الطريقة الأولى، اضغط أي مفتاح لتجربة الطريقة الثانية
echo If the first method doesn't work, press any key to try the second method
echo.
pause

echo.
echo الطريقة الثانية: خادم Laravel المدمج - Method 2: Laravel Built-in Server
echo.
echo تشغيل خادم Laravel المدمج - Starting Laravel built-in server...
echo الموقع سيكون متاح على - Website will be available at:
echo http://localhost:8000
echo.
echo اضغط Ctrl+C لإيقاف الخادم - Press Ctrl+C to stop the server
echo.

REM Try to start Laravel server (may have compatibility issues with PHP 8.0)
"%PHP_PATH%" -S localhost:8000 -t public

echo.
echo إذا واجهت مشاكل، تأكد من:
echo If you encounter issues, make sure:
echo 1. XAMPP Apache و MySQL يعملان - XAMPP Apache and MySQL are running
echo 2. قاعدة البيانات 'messultan' موجودة - Database 'messultan' exists
echo 3. إعدادات .env صحيحة - .env settings are correct
echo.
pause
