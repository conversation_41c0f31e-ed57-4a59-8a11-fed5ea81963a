[2025-06-04 01:41:38] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:210)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(135): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(122): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(645): Illuminate\\Log\\LogManager->driver()
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(274): Illuminate\\Log\\LogManager->error('syntax error, u...', Array)
#4 C:\\xampp\\htdocs\\manssultan\\app\\Exceptions\\Handler.php(45): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(ParseError))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(454): App\\Exceptions\\Handler->report(Object(ParseError))
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(157): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(ParseError))
#7 C:\\xampp\\htdocs\\manssultan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-04 01:41:38] laravel.ERROR: syntax error, unexpected token ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\manssultan\\vendor\\symfony\\finder\\Finder.php:647)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(86): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(63): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->getConfigurationFiles(Object(Illuminate\\Foundation\\Application))
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#4 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\manssultan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-04 01:45:56] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:210)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(135): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(122): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(645): Illuminate\\Log\\LogManager->driver()
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(274): Illuminate\\Log\\LogManager->error('syntax error, u...', Array)
#4 C:\\xampp\\htdocs\\manssultan\\app\\Exceptions\\Handler.php(45): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(ParseError))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(481): App\\Exceptions\\Handler->report(Object(ParseError))
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(136): Illuminate\\Foundation\\Http\\Kernel->reportException(Object(ParseError))
#7 C:\\xampp\\htdocs\\manssultan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#8 {main}
"} 
[2025-06-04 01:45:56] laravel.ERROR: syntax error, unexpected token ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\manssultan\\vendor\\symfony\\finder\\Finder.php:647)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(86): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(63): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->getConfigurationFiles(Object(Illuminate\\Foundation\\Application))
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#4 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\manssultan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#9 {main}
"} 
[2025-06-04 01:46:20] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:210)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(135): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(122): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(645): Illuminate\\Log\\LogManager->driver()
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(274): Illuminate\\Log\\LogManager->error('syntax error, u...', Array)
#4 C:\\xampp\\htdocs\\manssultan\\app\\Exceptions\\Handler.php(45): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(ParseError))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(481): App\\Exceptions\\Handler->report(Object(ParseError))
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(136): Illuminate\\Foundation\\Http\\Kernel->reportException(Object(ParseError))
#7 C:\\xampp\\htdocs\\manssultan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#8 {main}
"} 
[2025-06-04 01:46:20] laravel.ERROR: syntax error, unexpected token ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\manssultan\\vendor\\symfony\\finder\\Finder.php:647)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(86): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(63): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->getConfigurationFiles(Object(Illuminate\\Foundation\\Application))
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#4 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\manssultan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#9 {main}
"} 
[2025-06-04 01:46:38] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:210)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(135): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(122): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(645): Illuminate\\Log\\LogManager->driver()
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(274): Illuminate\\Log\\LogManager->error('syntax error, u...', Array)
#4 C:\\xampp\\htdocs\\manssultan\\app\\Exceptions\\Handler.php(45): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(ParseError))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(481): App\\Exceptions\\Handler->report(Object(ParseError))
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(136): Illuminate\\Foundation\\Http\\Kernel->reportException(Object(ParseError))
#7 C:\\xampp\\htdocs\\manssultan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#8 {main}
"} 
[2025-06-04 01:46:38] laravel.ERROR: syntax error, unexpected token ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\manssultan\\vendor\\symfony\\finder\\Finder.php:647)
[stacktrace]
#0 C:\\xampp\\htdocs\\manssultan\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#1 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(86): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#2 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(63): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->getConfigurationFiles(Object(Illuminate\\Foundation\\Application))
#3 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#4 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\manssultan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\manssultan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#9 {main}
"} 
